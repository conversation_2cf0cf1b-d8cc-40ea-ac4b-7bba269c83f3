/**
 * Human Behavior Simulation
 * Mô phỏng hành vi người dùng tự nhiên để tránh bot detection
 */

class HumanBehavior {
  constructor(page) {
    this.page = page;
    this.isActive = true;
  }

  /**
   * Enhanced human-like typing with realistic delays and patterns
   * @param {string} selector - Element selector
   * @param {string} text - Text to type
   * @param {Object} options - Typing options
   */
  async humanType(selector, text, options = {}) {
    const element = await this.page.locator(selector);

    // Add realistic focus delay
    await this.page.waitForTimeout(Math.random() * 200 + 100);
    await element.click();

    // Simulate thinking before typing
    await this.page.waitForTimeout(Math.random() * 800 + 300);

    // Clear existing text with realistic selection
    await element.selectText();
    await this.page.waitForTimeout(Math.random() * 100 + 50);
    await this.page.keyboard.press('Delete');

    // Enhanced typing simulation
    for (let i = 0; i < text.length; i++) {
      const char = text[i];

      // Simulate different typing speeds for different characters
      let baseDelay = 80;
      if (char === ' ') baseDelay = 120; // Slower for spaces
      if (/[A-Z]/.test(char)) baseDelay = 100; // Slower for capitals
      if (/[0-9]/.test(char)) baseDelay = 90; // Slightly slower for numbers
      if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(char)) baseDelay = 150; // Much slower for special chars

      await this.page.keyboard.type(char);

      // Add realistic variance to typing speed
      const delay = baseDelay + (Math.random() - 0.5) * 60;
      await this.page.waitForTimeout(Math.max(30, delay));

      // Occasional longer pauses (thinking/reading)
      if (Math.random() < 0.08) {
        await this.page.waitForTimeout(Math.random() * 800 + 400);
      }

      // Simulate realistic typos and corrections
      if (Math.random() < 0.015 && i > 0) {
        // Make a typo
        const wrongChar = String.fromCharCode(char.charCodeAt(0) + (Math.random() > 0.5 ? 1 : -1));
        await this.page.keyboard.press('Backspace');
        await this.page.waitForTimeout(Math.random() * 100 + 50);
        await this.page.keyboard.type(wrongChar);
        await this.page.waitForTimeout(Math.random() * 200 + 100);

        // Realize mistake and correct it
        await this.page.keyboard.press('Backspace');
        await this.page.waitForTimeout(Math.random() * 150 + 75);
        await this.page.keyboard.type(char);
        await this.page.waitForTimeout(Math.random() * 100 + 50);
      }

      // Simulate burst typing (faster for familiar words)
      if (i > 0 && Math.random() < 0.1) {
        // Type next 2-3 characters faster
        const burstLength = Math.min(Math.floor(Math.random() * 3) + 2, text.length - i - 1);
        for (let j = 1; j <= burstLength && i + j < text.length; j++) {
          i++;
          await this.page.keyboard.type(text[i]);
          await this.page.waitForTimeout(Math.random() * 30 + 20);
        }
      }
    }

    // Small pause after typing
    await this.page.waitForTimeout(Math.random() * 300 + 200);
  }

  /**
   * Simulate human-like mouse movement to element
   * @param {string} selector - Element selector
   * @param {Object} options - Movement options
   */
  async humanMove(selector, options = {}) {
    const element = await this.page.locator(selector);
    const box = await element.boundingBox();
    
    if (!box) return;
    
    // Get current mouse position (approximate)
    const currentPos = { x: 100, y: 100 }; // Default starting position
    
    // Calculate target position with some randomness
    const targetX = box.x + box.width / 2 + (Math.random() - 0.5) * 20;
    const targetY = box.y + box.height / 2 + (Math.random() - 0.5) * 20;
    
    // Move in steps to simulate human movement
    const steps = Math.floor(Math.random() * 10) + 5;
    for (let i = 0; i <= steps; i++) {
      const progress = i / steps;
      const x = currentPos.x + (targetX - currentPos.x) * progress;
      const y = currentPos.y + (targetY - currentPos.y) * progress;
      
      await this.page.mouse.move(x, y);
      await this.page.waitForTimeout(Math.random() * 20 + 10);
    }
  }

  /**
   * Simulate human-like clicking with movement
   * @param {string} selector - Element selector
   * @param {Object} options - Click options
   */
  async humanClick(selector, options = {}) {
    await this.humanMove(selector);
    
    // Small delay before clicking
    await this.page.waitForTimeout(Math.random() * 200 + 100);
    
    const element = await this.page.locator(selector);
    await element.click(options);
    
    // Small delay after clicking
    await this.page.waitForTimeout(Math.random() * 300 + 200);
  }

  /**
   * Simulate human-like scrolling
   * @param {Object} options - Scroll options
   */
  async humanScroll(options = {}) {
    const { direction = 'down', distance = 300, steps = 5 } = options;
    
    const stepDistance = distance / steps;
    
    for (let i = 0; i < steps; i++) {
      const delta = direction === 'down' ? stepDistance : -stepDistance;
      await this.page.mouse.wheel(0, delta);
      
      // Random delay between scroll steps
      await this.page.waitForTimeout(Math.random() * 200 + 100);
    }
    
    // Pause after scrolling (reading time)
    await this.page.waitForTimeout(Math.random() * 1000 + 500);
  }

  /**
   * Simulate reading behavior (pauses and small movements)
   * @param {number} duration - Reading duration in ms
   */
  async simulateReading(duration = 3000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < duration && this.isActive) {
      // Small random mouse movements
      const currentPos = await this.page.evaluate(() => {
        return { x: window.mouseX || 500, y: window.mouseY || 300 };
      });
      
      const newX = currentPos.x + (Math.random() - 0.5) * 50;
      const newY = currentPos.y + (Math.random() - 0.5) * 30;
      
      await this.page.mouse.move(newX, newY);
      
      // Random pause
      await this.page.waitForTimeout(Math.random() * 1000 + 500);
      
      // Occasional small scroll
      if (Math.random() < 0.3) {
        await this.page.mouse.wheel(0, (Math.random() - 0.5) * 100);
      }
    }
  }

  /**
   * Simulate form filling behavior
   * @param {Array} fields - Array of field objects {selector, value}
   */
  async fillFormHumanLike(fields) {
    for (const field of fields) {
      // Look around before focusing on field
      await this.simulateReading(Math.random() * 1000 + 500);
      
      // Move to field and click
      await this.humanClick(field.selector);
      
      // Type the value
      await this.humanType(field.selector, field.value);
      
      // Small pause after filling field
      await this.page.waitForTimeout(Math.random() * 1000 + 500);
    }
  }

  /**
   * Simulate page exploration (looking around)
   * @param {number} duration - Exploration duration in ms
   */
  async explorePage(duration = 5000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < duration && this.isActive) {
      // Random mouse movements
      const x = Math.random() * 800 + 100;
      const y = Math.random() * 600 + 100;
      
      await this.page.mouse.move(x, y);
      await this.page.waitForTimeout(Math.random() * 1500 + 500);
      
      // Occasional scroll
      if (Math.random() < 0.4) {
        await this.humanScroll({
          direction: Math.random() < 0.5 ? 'down' : 'up',
          distance: Math.random() * 200 + 100,
          steps: Math.floor(Math.random() * 3) + 2
        });
      }
    }
  }

  /**
   * Add random delays to make actions seem more human
   * @param {number} baseDelay - Base delay in ms
   * @param {number} variance - Variance in ms
   */
  async randomDelay(baseDelay = 1000, variance = 500) {
    const delay = baseDelay + (Math.random() - 0.5) * variance;
    await this.page.waitForTimeout(Math.max(100, delay));
  }

  /**
   * Simulate hesitation before important actions
   * @param {string} actionType - Type of action (click, submit, etc.)
   */
  async simulateHesitation(actionType = 'click') {
    // Longer hesitation for important actions
    const hesitationTime = actionType === 'submit' ? 
      Math.random() * 2000 + 1000 : 
      Math.random() * 1000 + 500;
    
    // Small mouse movements during hesitation
    for (let i = 0; i < 3; i++) {
      const currentPos = await this.page.evaluate(() => {
        return { x: window.mouseX || 500, y: window.mouseY || 300 };
      });
      
      await this.page.mouse.move(
        currentPos.x + (Math.random() - 0.5) * 20,
        currentPos.y + (Math.random() - 0.5) * 20
      );
      
      await this.page.waitForTimeout(hesitationTime / 3);
    }
  }

  /**
   * Stop all human behavior simulation
   */
  stop() {
    this.isActive = false;
  }

  /**
   * Simulate realistic page interaction patterns
   */
  async simulatePageInteraction() {
    if (!this.isActive) return;

    // Simulate reading behavior
    await this.page.waitForTimeout(Math.random() * 1000 + 500);

    // Random small scrolls
    for (let i = 0; i < Math.floor(Math.random() * 3) + 1; i++) {
      await this.page.mouse.wheel(0, Math.random() * 200 + 100);
      await this.page.waitForTimeout(Math.random() * 1500 + 800);
    }

    // Simulate mouse movements
    for (let i = 0; i < Math.floor(Math.random() * 5) + 2; i++) {
      const x = Math.random() * await this.page.evaluate(() => window.innerWidth);
      const y = Math.random() * await this.page.evaluate(() => window.innerHeight);

      await this.page.mouse.move(x, y, { steps: Math.floor(Math.random() * 10) + 5 });
      await this.page.waitForTimeout(Math.random() * 800 + 400);
    }
  }

  /**
   * Simulate realistic login behavior patterns
   * @param {string} emailSelector - Email input selector
   * @param {string} passwordSelector - Password input selector
   * @param {string} email - Email to type
   * @param {string} password - Password to type
   */
  async simulateLoginBehavior(emailSelector, passwordSelector, email, password) {
    // Look around the page first
    await this.simulatePageInteraction();

    // Focus on email field with hesitation
    await this.simulateHesitation('focus');
    await this.humanType(emailSelector, email);

    // Small pause before moving to password
    await this.page.waitForTimeout(Math.random() * 800 + 400);

    // Move to password field
    await this.humanMove(passwordSelector);
    await this.page.waitForTimeout(Math.random() * 300 + 200);

    // Type password (usually faster as it's memorized)
    const passwordElement = await this.page.locator(passwordSelector);
    await passwordElement.click();
    await this.page.waitForTimeout(Math.random() * 200 + 100);

    // Type password with consistent speed (muscle memory)
    for (let i = 0; i < password.length; i++) {
      await this.page.keyboard.type(password[i]);
      await this.page.waitForTimeout(Math.random() * 40 + 60); // Faster, more consistent
    }

    // Pause before submitting (double-checking)
    await this.page.waitForTimeout(Math.random() * 1200 + 800);
  }

  /**
   * Simulate realistic button clicking with pre-click behavior
   * @param {string} selector - Button selector
   */
  async humanClickButton(selector) {
    // Move to button area
    await this.humanMove(selector);

    // Hesitate before clicking
    await this.simulateHesitation('click');

    // Click with slight offset for realism
    const element = await this.page.locator(selector);
    const box = await element.boundingBox();

    if (box) {
      const x = box.x + box.width * (0.3 + Math.random() * 0.4);
      const y = box.y + box.height * (0.3 + Math.random() * 0.4);

      await this.page.mouse.click(x, y);
    } else {
      await element.click();
    }

    // Small pause after click
    await this.page.waitForTimeout(Math.random() * 200 + 100);
  }

  /**
   * Start human behavior simulation
   */
  start() {
    this.isActive = true;
  }

  /**
   * Stop all human behavior simulation
   */
  stop() {
    this.isActive = false;
  }
}

module.exports = HumanBehavior;
