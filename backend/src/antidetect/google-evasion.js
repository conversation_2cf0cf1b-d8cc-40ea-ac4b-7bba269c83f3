/**
 * Google-specific Evasion Techniques
 * <PERSON><PERSON><PERSON><PERSON> về bypass Google's advanced bot detection systems
 */

class GoogleEvasion {
  constructor() {
    this.isActive = true;
    this.mouseTracker = { x: 0, y: 0, movements: 0 };
    this.keyboardTracker = { keystrokes: 0, lastKey: null };
    this.behaviorPatterns = [];
  }

  /**
   * Inject advanced Google evasion scripts
   * @param {Page} page - Playwright page object
   * @param {Object} persona - Persona object
   */
  async injectGoogleEvasion(page, persona) {
    await page.addInitScript(() => {
      // === ADVANCED GOOGLE BOT DETECTION BYPASS ===
      
      // 1. Bypass Google's advanced fingerprinting
      (function() {
        // Override Performance API to hide automation signatures
        if (window.PerformanceObserver) {
          const originalObserve = PerformanceObserver.prototype.observe;
          PerformanceObserver.prototype.observe = function(options) {
            if (options && options.entryTypes) {
              // Filter out automation-related entries
              options.entryTypes = options.entryTypes.filter(type => 
                !['navigation', 'resource', 'measure'].includes(type)
              );
            }
            return originalObserve.call(this, options);
          };
        }

        // Override timing functions to add realistic variance
        if (window.performance && window.performance.now) {
          const originalNow = window.performance.now;
          let timeOffset = Math.random() * 5 + 2; // 2-7ms offset
          window.performance.now = function() {
            timeOffset += (Math.random() - 0.5) * 0.1; // Small drift
            return originalNow.call(this) + timeOffset;
          };
        }

        // Bypass Google's entropy detection
        if (window.crypto && window.crypto.getRandomValues) {
          const originalGetRandomValues = window.crypto.getRandomValues;
          window.crypto.getRandomValues = function(array) {
            const result = originalGetRandomValues.call(this, array);
            // Add slight predictable pattern to mimic human-generated entropy
            for (let i = 0; i < array.length; i += 4) {
              if (array[i] !== undefined) {
                array[i] = (array[i] + i) % 256;
              }
            }
            return result;
          };
        }
      })();

      // 2. Enhanced Google OAuth and Login Detection Bypass
      (function() {
        // Bypass Google's OAuth flow detection
        const originalPostMessage = window.postMessage;
        window.postMessage = function(message, targetOrigin, transfer) {
          // Add delay to OAuth messages to appear more human
          if (typeof message === 'string' && message.includes('oauth')) {
            setTimeout(() => {
              originalPostMessage.call(this, message, targetOrigin, transfer);
            }, Math.random() * 100 + 50);
          } else {
            originalPostMessage.call(this, message, targetOrigin, transfer);
          }
        };

        // Override Google's gapi library detection
        Object.defineProperty(window, 'gapi', {
          get: function() {
            return {
              load: function(api, callback) {
                // Simulate realistic loading time
                setTimeout(callback, Math.random() * 200 + 100);
              },
              auth2: {
                init: function() {
                  return Promise.resolve({
                    isSignedIn: { get: () => false },
                    signIn: function() { return Promise.resolve(); }
                  });
                }
              }
            };
          },
          configurable: true
        });

        // Bypass Google's advanced iframe detection
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
          const element = originalCreateElement.call(this, tagName);
          if (tagName.toLowerCase() === 'iframe') {
            // Make iframe appear more legitimate
            Object.defineProperty(element, 'contentWindow', {
              get: function() {
                return {
                  location: { 
                    href: 'about:blank',
                    origin: window.location.origin 
                  },
                  document: { 
                    readyState: 'complete',
                    domain: window.location.hostname
                  },
                  navigator: window.navigator,
                  screen: window.screen
                };
              }
            });
          }
          return element;
        };
      })();

      // 3. Bypass Google's Mouse and Keyboard Detection
      (function() {
        let mouseMovements = [];
        let keyboardEvents = [];
        
        // Track and simulate realistic mouse patterns
        document.addEventListener('mousemove', function(e) {
          mouseMovements.push({
            x: e.clientX,
            y: e.clientY,
            timestamp: Date.now()
          });
          
          // Keep only recent movements
          if (mouseMovements.length > 50) {
            mouseMovements = mouseMovements.slice(-25);
          }
        });

        // Simulate realistic keyboard patterns
        document.addEventListener('keydown', function(e) {
          keyboardEvents.push({
            key: e.key,
            timestamp: Date.now(),
            duration: Math.random() * 100 + 50
          });
          
          if (keyboardEvents.length > 20) {
            keyboardEvents = keyboardEvents.slice(-10);
          }
        });

        // Override Google's event listener detection
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
          // Add slight delay to event registration for Google APIs
          if (typeof listener === 'function' && 
              (type === 'load' || type === 'DOMContentLoaded')) {
            const wrappedListener = function(event) {
              setTimeout(() => listener.call(this, event), Math.random() * 10 + 5);
            };
            return originalAddEventListener.call(this, type, wrappedListener, options);
          }
          return originalAddEventListener.call(this, type, listener, options);
        };
      })();

      // 4. Advanced Network Request Spoofing
      (function() {
        // Override XMLHttpRequest for Google services
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
          if (typeof url === 'string' && 
              (url.includes('google') || url.includes('gstatic') || url.includes('googleapis'))) {
            // Add realistic headers for Google requests
            this.addEventListener('readystatechange', function() {
              if (this.readyState === 1) { // OPENED
                this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                this.setRequestHeader('Cache-Control', 'no-cache');
                this.setRequestHeader('Pragma', 'no-cache');
              }
            });
          }
          return originalXHROpen.call(this, method, url, async, user, password);
        };

        // Enhanced fetch override for Google APIs
        const originalFetch = window.fetch;
        window.fetch = function(input, init) {
          const url = typeof input === 'string' ? input : input.url;
          
          if (url && (url.includes('google') || url.includes('gstatic') || url.includes('googleapis'))) {
            // Add realistic delay for Google API calls
            return new Promise((resolve) => {
              setTimeout(() => {
                // Add realistic headers
                const enhancedInit = {
                  ...init,
                  headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'cross-site',
                    ...init?.headers
                  }
                };
                resolve(originalFetch.call(this, input, enhancedInit));
              }, Math.random() * 150 + 75);
            });
          }
          
          return originalFetch.call(this, input, init);
        };
      })();

      // 5. Bypass Google's Canvas and WebGL Detection
      (function() {
        // Enhanced canvas fingerprinting protection
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(type, quality) {
          const context = this.getContext('2d');
          if (context) {
            // Add subtle noise that varies by session
            const imageData = context.getImageData(0, 0, this.width, this.height);
            const data = imageData.data;
            
            for (let i = 0; i < data.length; i += 4) {
              // Add minimal noise to RGB channels
              data[i] = Math.min(255, data[i] + (Math.random() - 0.5) * 2);
              data[i + 1] = Math.min(255, data[i + 1] + (Math.random() - 0.5) * 2);
              data[i + 2] = Math.min(255, data[i + 2] + (Math.random() - 0.5) * 2);
            }
            
            context.putImageData(imageData, 0, 0);
          }
          
          return originalToDataURL.call(this, type, quality);
        };

        // WebGL fingerprinting protection
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
          const result = originalGetParameter.call(this, parameter);
          
          // Slightly modify specific parameters that Google checks
          if (parameter === this.RENDERER) {
            return result + ' (Modified)';
          }
          if (parameter === this.VENDOR) {
            return result.replace(/Google Inc\./g, 'Google Inc');
          }
          
          return result;
        };
      })();

      // 6. Simulate Human-like Behavior Patterns
      (function() {
        // Simulate realistic focus/blur patterns
        let focusCount = 0;
        const originalFocus = HTMLElement.prototype.focus;
        HTMLElement.prototype.focus = function() {
          focusCount++;
          // Add slight delay to focus events
          setTimeout(() => {
            originalFocus.call(this);
          }, Math.random() * 20 + 10);
        };

        // Simulate realistic scroll behavior
        let lastScrollTime = 0;
        const originalScrollTo = window.scrollTo;
        window.scrollTo = function(x, y) {
          const now = Date.now();
          const timeDiff = now - lastScrollTime;
          
          // Prevent too-fast scrolling (bot-like behavior)
          if (timeDiff < 50) {
            setTimeout(() => {
              originalScrollTo.call(this, x, y);
            }, 50 - timeDiff);
          } else {
            originalScrollTo.call(this, x, y);
          }
          
          lastScrollTime = now;
        };

        // Add periodic "human" activities
        setInterval(() => {
          // Simulate small mouse movements
          if (Math.random() < 0.3) {
            const event = new MouseEvent('mousemove', {
              clientX: Math.random() * window.innerWidth,
              clientY: Math.random() * window.innerHeight,
              bubbles: true
            });
            document.dispatchEvent(event);
          }
          
          // Simulate occasional key presses (like Ctrl, Alt)
          if (Math.random() < 0.1) {
            const event = new KeyboardEvent('keydown', {
              key: Math.random() < 0.5 ? 'Control' : 'Alt',
              bubbles: true
            });
            document.dispatchEvent(event);
          }
        }, 2000 + Math.random() * 3000);
      })();

      console.log('🔒 Google Evasion techniques activated');
    });
  }

  /**
   * Simulate realistic human interaction patterns
   * @param {Page} page - Playwright page object
   */
  async simulateHumanPatterns(page) {
    // Simulate realistic page interaction
    await page.evaluate(() => {
      // Simulate reading behavior
      const simulateReading = () => {
        const scrollHeight = document.body.scrollHeight;
        const viewportHeight = window.innerHeight;
        const scrollSteps = Math.floor(scrollHeight / viewportHeight) + 1;
        
        let currentStep = 0;
        const scrollInterval = setInterval(() => {
          if (currentStep < scrollSteps) {
            window.scrollTo(0, currentStep * viewportHeight);
            currentStep++;
          } else {
            clearInterval(scrollInterval);
          }
        }, 1000 + Math.random() * 2000);
      };

      // Start reading simulation after page load
      if (document.readyState === 'complete') {
        setTimeout(simulateReading, 1000 + Math.random() * 2000);
      } else {
        window.addEventListener('load', () => {
          setTimeout(simulateReading, 1000 + Math.random() * 2000);
        });
      }
    });
  }

  /**
   * Add realistic delays before important actions
   * @param {number} baseDelay - Base delay in milliseconds
   * @returns {Promise}
   */
  async addHumanDelay(baseDelay = 1000) {
    const variance = baseDelay * 0.3; // 30% variance
    const delay = baseDelay + (Math.random() - 0.5) * variance;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Stop all evasion activities
   */
  stop() {
    this.isActive = false;
  }
}

module.exports = GoogleEvasion;
