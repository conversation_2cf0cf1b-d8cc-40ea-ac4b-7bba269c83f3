{"accessibility": {"captions": {"soda_registered_language_packs": ["en-US"]}}, "autofill": {"ablation_seed": "VFqNzU6p4i4="}, "breadcrumbs": {"enabled": false, "enabled_time": "13395899522812211"}, "browser": {"whats_new": {"enabled_order": ["PdfSearchify"]}}, "chrome_labs_activation_threshold": 88, "chrome_labs_new_badge_dict": {}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "management": {"platform": {"enterprise_mdm_mac": 0}}, "network_time": {"network_time_mapping": {"local": 1751425923572.247, "network": 1751425923646.0, "ticks": ************.0, "uncertainty": 10241909.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "138.0.7204.23", "model_crash_count": 0, "performance_class": 7, "performance_class_version": "138.0.7204.23"}}, "performance_intervention": {"last_daily_sample": "13395899523062634"}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.****************}, "profile": {"info_cache": {"Default": {"active_time": **********.576057, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_26", "background_apps": false, "default_avatar_fill_color": -********, "default_avatar_stroke_color": -3684409, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Your Chromium", "profile_color_seed": -5715974, "profile_highlight_color": -********, "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "session_id_generator_last_value": "*********", "signin": {"active_accounts_last_emitted": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_frozen": 0, "discards_proactive": 0, "discards_suggested": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 3, "reloads_external": 0, "reloads_frozen": 0, "reloads_proactive": 0, "reloads_suggested": 0, "reloads_urgent": 0, "total_tab_count_max": 4, "window_count_max": 2}, "uninstall_metrics": {"installation_date2": "**********"}, "user_experience_metrics": {"client_id2": "bd324069-626c-40e7-a306-d16ca96f69cf", "client_id_timestamp": "**********", "limited_entropy_randomization_source": "0BA6CAD73EF871F86793A8A80D6059DC", "log_record_id": 3, "low_entropy_source3": 4347, "machine_id": 3514659, "pseudo_low_entropy_source": 53, "session_id": 2, "stability": {"browser_last_live_timestamp": "*****************", "exited_cleanly": true, "saved_system_profile": "CM+zosIGEhYxMzguMC43MjA0LjIzLTY0LWRldmVsGLDBksMGIgVlbi1VUyoSCghNYWMgT1MgWBIGMTUuNS4wMlMKBWFybTY0EICAASIITWFjMTYsMTAoATCAFDigC0IKCAAQABoAMgA6AGUAAIA/ahEKB3Vua25vd24QABgKIAAoBoIBAIoBAKoBBkFSTV82NLABAUoKDZK3V7MV3xdKP0oKDQUO8PQVgI19ylAAWgBiBENIRkFqCAgAEAA4AEAAgAGwwZLDBpgBAPgB+yGAAv///////////wGIAgCSAiRiZDMyNDA2OS02MjZjLTQwZTctYTMwNi1kMTZjYTk2ZjY5Y2aoAjWyAuABZB450l6K9GKeSYlsnstQPbW1No834q1+xYgAKn7IsGzbmzOpC4+9L9jALbH+W8+HRnQ0Ou5qLX8qfizFP5f73BkM2wlMtFQTSf0f/2sDUFiw3veNzmquUFQfjfI4qugDymV+G5gUG6jb+1MuAqZcs2N3Hn15S6ppHgxGjeP7Ti6EKwVr52w1Yt/vrGf4oogvb9lBYU76y5jr6+UKyEBE/rUQh9LWxgqmG/UF8TkL3aI1ReaaJ0SDSS28C5uBVr1iY+tt3QVxIffPaDN/U5++z7Mihb9C4OPrhIu4/0zzDV3xApf20aicIg1E", "saved_system_profile_hash": "64101306638EC5AF5B6F3AA652908789C71735AD", "stats_buildtime": "1749588431", "stats_version": "138.0.7204.23-64-devel"}}, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "42", "was": {"restarted": false}}