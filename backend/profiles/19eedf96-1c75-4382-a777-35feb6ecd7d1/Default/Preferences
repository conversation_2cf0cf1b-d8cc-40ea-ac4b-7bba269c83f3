{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "arm64", "shortcuts_version": 7}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1223, "left": 22, "maximized": false, "right": 1704, "top": 47, "work_area_bottom": 1440, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 25}, "window_placement_popup": {"bottom": 973, "left": 612, "maximized": false, "right": 1114, "top": 297, "work_area_bottom": 1440, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 25}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "a53f3a27-2e8c-4031-8ce1-0546e1048cef", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.23"}, "gaia_cookie": {"changed_time": **********.58496, "hash": "iMt8YNFWe/XYgQSvq1/LVmvrmbg=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"<PERSON><PERSON> <PERSON><PERSON>\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-6n8RCvClelI/AAAAAAAAAAI/AAAAAAAAAAA/kAccDHFYkwE/s48-c/photo.jpg\",1,1,0,null,1,\"116601369743478654806\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.macosx"}, "google": {"services": {"signin_scoped_device_id": "a86a0a52-6ed9-4a5d-900a-47b0c294854e"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}}, "https_upgrade_navigations": {"2025-07-02": 20}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "13395899522918361", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13395899522916035", "recent_session_start_times": ["13395899522916035"], "session_last_active_time": "13395909922429170", "session_start_time": "13395899522916035"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "EV2d0s7dnrsoe2fqFIyqwL/+WYB7lVqOEq4+5P4yfGzfX2Y0kMePDuVUmOiuAatm2RygSukL3oW20/722Qqtuw=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {"https://[*.]google.com,https://[*.]tiktok.com": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": 1}}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.tiktok.com:443,*": {"last_modified": "13395899527026723", "setting": {"https://www.tiktok.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "*****************"}}, "https://www.tiktok.com/foryou": {"couldShowBannerEvents": 1.339589952702672e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com.vn:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [4, 5, 9, 10, 11, 13, 14, 15, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]google.com.vn,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]tiktok.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]whatismyipaddress.com,*": {"last_modified": "*****************", "setting": {}}, "https://httpbin.org,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com.vn:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://accounts.google.com:443,*": {"expiration": "********952355216", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 6}}, "https://httpbin.org:443,*": {"expiration": "********836314904", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://whatismyipaddress.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.tiktok.com:443,*": {"expiration": "********955950331", "last_modified": "13395909955950340", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 6}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395903772089696e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.0, "rawScore": 6.0}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.339590994234842e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.****************, "rawScore": 3.****************}}, "https://httpbin.org:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395909834669484e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.0, "rawScore": 6.0}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395903776907524e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.1, "rawScore": 5.1}}, "https://www.tiktok.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.33959099028233e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.***************, "rawScore": 8.***************}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.23", "creation_time": "13395899522830979", "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13395909942348420", "last_time_obsolete_http_credentials_removed": 1751425982.879733, "last_time_password_store_metrics_reported": 1751425952.878567, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chromium", "password_hash_data_list": [{"hash": "djEw2jPxGigKh4nsWje1IQhocA==", "is_gaia": "djEwlYECCo/zlDoLthwIKSFM0A==", "last_signin": 1751426320.421691, "salt_length": "djEwJ0J2JZjvQ+/lFf1450a7F/ltCPZ4x/9sE7wLxSoyIdM=", "username": "djEwgtZzuS67He2exzvjrS57DuRWUgbk1MKnZMI22yyb0cU="}], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13395899522", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQmYPUxv7v5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEKaD1Mb+7+UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13395801599000000", "uma_in_sql_start_time": "13395899522885829"}, "sessions": {"event_log": [{"crashed": false, "time": "13395899522882032", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 3, "time": "13395899933523574", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395899945816941", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395899948343507", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395903740948021", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://www.tiktok.com/foryou": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}, "webauthn": {"touchid": {"metadata_secret": "PRDG6AJc488M4p479ZjvtbI/42QE0prDq/S8xRet70M="}}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"flow\",\"temp mail\",\"you\",\"translate\",\"augment code ai\",\"whatismyip\",\"chrome extension\"],[\"history\",\"history\",\"history\",\"history\",\"history\",\"history\",\"history\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggestdetail\":[{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dflow\\u0026deltok\\u003dAMc44K4eA3wQM2KTuLizSW3l1p4kBH_SRw\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dtemp+mail\\u0026deltok\\u003dAMc44K7JL_mMsUFddonQCPIawVVyXFZ_5A\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dyou\\u0026deltok\\u003dAMc44K5GnXlRh_g9voGYBnochnLhX5E5hw\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dtranslate\\u0026deltok\\u003dAMc44K73cbCxGxeaJAf3S5aZorlK46IL7g\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003daugment+code+ai\\u0026deltok\\u003dAMc44K5QNDPTqTNCRJqrnSZp1CQuM2NuSw\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dwhatismyip\\u0026deltok\\u003dAMc44K74yskECAaeJn_D65lEQNQyE_Y-xw\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dchrome+extension\\u0026deltok\\u003dAMc44K7aFrazqgk-o-tZKeLqQg0eZzkCEA\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000}],\"google:suggesteventid\":\"2067397195746098790\",\"google:suggestrelevance\":[606,605,604,603,602,601,600],\"google:suggestsubtypes\":[[362,39],[362,39],[362,39],[362,39],[362,39],[362,39],[362,39]],\"google:suggesttype\":[\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\"],\"google:verbatimrelevance\":851}]", "cachedresults_with_url": {"https://www.google.com/search?q=what+is+my+ip&oq=what+is&gs_lcrp=EgZjaHJvbWUqBwgBEAAYgAQyBggAEEUYOTIHCAEQABiABDIHCAIQABiABDIHCAMQABiABDIQCAQQABiDARixAxiABBiKBTIHCAUQABiABDIHCAYQABiABDIHCAcQABiABDIHCAgQABiABDIHCAkQABiABNIBCDQ5MTFqMGo3qAIAsAIA&sourceid=chrome&ie=UTF-8": ")]}'\n[\"\",[\"port checker\",\"nordvpn\",\"my ip address\",\"whats my ip\",\"ip lookup\",\"test\",\"what is my ipv4 address\",\"ip chicken\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"Ch8IwLgCEhkKF1TDrG0ga2nhur9tIGfhuqduIMSRw6J5CjsIkE4SNgo0TGnDqm4gcXVhbiDEkeG6v24gbuG7mWkgZHVuZyB0w6xtIGtp4bq/bSBn4bqnbiDEkcOieQ\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000}],\"google:suggesteventid\":\"-3577683962842484864\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}}