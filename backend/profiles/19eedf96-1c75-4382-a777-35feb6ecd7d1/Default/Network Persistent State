{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 1, "host": "www.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 3, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "lh3.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "broken_count": 2, "host": "encrypted-tbn0.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "apis.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 3, "host": "whatismyipaddress.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "fonts.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "app.fusebox.fm", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "cdn.onesignal.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "a.pub.network", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 2, "host": "securepubads.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "onesignal.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "merequartz.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "cdn.confiant-integrations.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "c.pub.network", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "na.edge.optable.co", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "zipthelake.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "storage.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "ssl.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 3, "broken_until": "**********", "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "broken_count": 2, "broken_until": "**********", "host": "accounts.youtube.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "broken_until": "**********", "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 3, "broken_until": "**********", "host": "lh3.googleusercontent.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-common.ibytedtos.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL2dvb2dsZS5jb20udm4AAAA=", false, 0], "server": "https://accounts.google.com.vn", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb-sg.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs-sg.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://starling-sg.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://starling-va.tiktokv.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 225111}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2dzdGF0aWMuY29tAA==", false, 0], "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495776537657", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 226682}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495778635570", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://a.omappapi.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://app.fusebox.fm", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://map.whatismyipaddress.info", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://api.omappapi.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495784785725", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://d.pub.network", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495785227273", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cdn.onesignal.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://whatismyipaddress.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://static.libsyn.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://www.clarity.ms", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495788141899", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495788222129", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495788682898", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://optimise.net", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://static.adsafeprotected.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://onesignal.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495790164205", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://freestar.solutions.cdn.optable.co", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://aax.amazon-adsystem.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495791135627", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", true, 0], "server": "https://tpc.googlesyndication.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://merequartz.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://a.pub.network", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cdn.confiant-integrations.net", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://c.pub.network", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://na.edge.optable.co", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://securepubads.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://c.amazon-adsystem.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://btloader.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395990193211395", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://metrics.rapidedge.io", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cdn.hadronid.net", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://zipthelake.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", true, 0], "server": "https://securepubads.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", true, 0], "server": "https://8783771c7c06961aedfc5f9ed1fe345f.safeframe.googlesyndication.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cmp.inmobi.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398495794252868", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://pagead2.googlesyndication.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs.tiktokw.us", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2h0dHBiaW4ub3JnAA==", false, 0], "server": "https://httpbin.org", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website.neutral.ttwstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398501842259068", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "network_stats": {"srtt": 230272}, "server": "https://storage.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://login-no1a.www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://web-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-sg.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website-login.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 228557}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398501909793446", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 226974}, "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398501911647818", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398501912780921", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 227316}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 228192}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 230445}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 227058}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 232462}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 236332}, "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mon.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs-va.tiktokv.com", "supports_spdy": true}], "supports_quic": {"address": "2606:4700:110:8041:7f54:ade7:c537:ff6a", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G"}}}